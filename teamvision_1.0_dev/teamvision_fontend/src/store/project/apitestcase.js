const state = {
  selectApiCase: null,
  // 标签页管理
  requestTabs: [],
  activeRequestId: null,
  tabCounter: 0,
  isCreatingNewRequest: false,

  // 集合数据
  collections: [],
  collectionsLoading: false,
  collectionsTree: [],

  // 环境数据
  environments: [],
  selectedEnvironment: null,
  selectedEnvironmentId: null,

  // 执行历史
  executionHistory: [],

  // UI 状态
  leftPanelWidth: 280,
  activeNavTab: 'collections',

  // 响应数据
  currentResponse: null,
  isExecuting: false
}

const getters = {
  // 获取当前活动的请求标签页
  currentRequestTab: (state) => {
    return state.requestTabs.find(tab => tab.id === state.activeRequestId)
  },

  // 获取标签页数量
  tabCount: (state) => state.requestTabs.length,

  // 检查是否有未保存的标签页
  hasUnsavedTabs: (state) => {
    return state.requestTabs.some(tab => tab.isNew && tab.hasChanges)
  },

  // 获取集合树结构
  collectionsTreeData: (state) => state.collectionsTree,

  // 获取当前选中的环境
  currentEnvironment: (state) => {
    return state.environments.find(env => env.id === state.selectedEnvironmentId)
  }
}

const mutations = {
  setSelectApiCase(state, caseID) {
    state.selectApiCase = caseID
  },

  // 标签页管理 mutations
  SET_REQUEST_TABS(state, tabs) {
    state.requestTabs = tabs
  },

  ADD_REQUEST_TAB(state, tab) {
    // 检查是否已存在相同ID的标签页
    const existingIndex = state.requestTabs.findIndex(t => t.id === tab.id)
    if (existingIndex === -1) {
      state.requestTabs.push(tab)
    }
  },

  REMOVE_REQUEST_TAB(state, tabId) {
    const index = state.requestTabs.findIndex(tab => tab.id === tabId)
    if (index > -1) {
      state.requestTabs.splice(index, 1)
    }
  },

  UPDATE_REQUEST_TAB(state, { tabId, data }) {
    const tab = state.requestTabs.find(t => t.id === tabId)
    if (tab) {
      Object.assign(tab, data)
    }
  },

  SET_ACTIVE_REQUEST_ID(state, requestId) {
    state.activeRequestId = requestId
  },

  INCREMENT_TAB_COUNTER(state) {
    state.tabCounter++
  },

  SET_CREATING_REQUEST_STATUS(state, status) {
    state.isCreatingNewRequest = status
  },

  // 集合管理 mutations
  SET_COLLECTIONS(state, collections) {
    state.collections = collections
  },

  SET_COLLECTIONS_LOADING(state, loading) {
    state.collectionsLoading = loading
  },

  SET_COLLECTIONS_TREE(state, tree) {
    state.collectionsTree = tree
  },

  ADD_COLLECTION(state, collection) {
    state.collections.push(collection)
  },

  ADD_COLLECTION_TO_FRONT(state, collection) {
    state.collections.unshift(collection)
  },

  UPDATE_COLLECTION(state, { id, data }) {
    const collection = state.collections.find(c => c.id === id)
    if (collection) {
      Object.assign(collection, data)
    }
  },

  REMOVE_COLLECTION(state, id) {
    const index = state.collections.findIndex(c => c.id === id)
    if (index > -1) {
      state.collections.splice(index, 1)
    }
  },

  // 环境管理 mutations
  SET_ENVIRONMENTS(state, environments) {
    state.environments = environments
  },

  SET_SELECTED_ENVIRONMENT(state, { id, name }) {
    state.selectedEnvironmentId = id
    state.selectedEnvironment = name
  },

  SET_SELECTED_ENVIRONMENT_ID(state, environmentId) {
    state.selectedEnvironmentId = environmentId
    const environment = state.environments.find(env => env.id === environmentId)
    state.selectedEnvironment = environment ? environment.name : null
  },

  ADD_ENVIRONMENT(state, environment) {
    state.environments.push(environment)
  },

  UPDATE_ENVIRONMENT(state, { id, data }) {
    const env = state.environments.find(e => e.id === id)
    if (env) {
      Object.assign(env, data)
    }
  },

  REMOVE_ENVIRONMENT(state, id) {
    const index = state.environments.findIndex(e => e.id === id)
    if (index > -1) {
      state.environments.splice(index, 1)
    }
  },

  // UI 状态 mutations
  SET_LEFT_PANEL_WIDTH(state, width) {
    state.leftPanelWidth = width
  },

  SET_ACTIVE_NAV_TAB(state, tab) {
    state.activeNavTab = tab
  },

  // 响应数据 mutations
  SET_CURRENT_RESPONSE(state, response) {
    state.currentResponse = response
  },

  SET_EXECUTING_STATUS(state, status) {
    state.isExecuting = status
  },

  // 执行历史 mutations
  ADD_EXECUTION_HISTORY(state, history) {
    state.executionHistory.unshift(history)
    // 限制历史记录数量
    if (state.executionHistory.length > 100) {
      state.executionHistory = state.executionHistory.slice(0, 100)
    }
  },

  SET_EXECUTION_HISTORY(state, history) {
    state.executionHistory = history
  }
}

const actions = {
  // 标签页管理 actions
  async createNewRequestTab({ commit, state }, { name, method = 'GET', url = '' } = {}) {
    commit('SET_CREATING_REQUEST_STATUS', true)

    try {
      commit('INCREMENT_TAB_COUNTER')
      const newId = `request_${Date.now()}_${state.tabCounter}`

      const newTab = {
        id: newId,
        name: name || `New Request ${state.tabCounter}`,
        method,
        url,
        description: '',
        headers: {},
        query_params: {},
        path_variables: {},
        body_type: 'none',
        body_data: {},
        auth_type: 'none',
        auth_config: {},
        pre_request_script: '',
        post_request_script: '',
        test_assertions: [],
        isNew: true,
        hasChanges: false,
        collection: null
      }

      commit('ADD_REQUEST_TAB', newTab)
      commit('SET_ACTIVE_REQUEST_ID', newId)

      return newTab
    } finally {
      // 延迟重置状态，避免快速点击问题
      setTimeout(() => {
        commit('SET_CREATING_REQUEST_STATUS', false)
      }, 300)
    }
  },

  switchRequestTab({ commit, state }, tabId) {
    const tab = state.requestTabs.find(t => t.id === tabId)
    if (tab) {
      commit('SET_ACTIVE_REQUEST_ID', tabId)
      return tab
    }
    return null
  },

  closeRequestTab({ commit, state }, tabId) {
    const tabIndex = state.requestTabs.findIndex(t => t.id === tabId)
    if (tabIndex === -1) return

    commit('REMOVE_REQUEST_TAB', tabId)

    // 如果关闭的是当前活动标签页，需要切换到其他标签页
    if (state.activeRequestId === tabId) {
      if (state.requestTabs.length > 0) {
        // 优先选择右边的标签页，如果没有则选择左边的
        const newActiveIndex = tabIndex < state.requestTabs.length ? tabIndex : tabIndex - 1
        const newActiveTab = state.requestTabs[newActiveIndex]
        commit('SET_ACTIVE_REQUEST_ID', newActiveTab?.id || null)
      } else {
        commit('SET_ACTIVE_REQUEST_ID', null)
      }
    }
  },

  updateRequestTab({ commit }, { tabId, data }) {
    commit('UPDATE_REQUEST_TAB', { tabId, data: { ...data, hasChanges: true } })
  },

  // 环境管理 actions
  async loadEnvironments({ commit }, projectId) {
    try {
      // TODO: 实现加载环境的 API 调用
      // const response = await api.getEnvironments(projectId)
      // commit('SET_ENVIRONMENTS', response.data)

      // 临时模拟数据，实际项目中需要替换为真实的 API 调用
      const mockEnvironments = [
        { id: 1, name: '开发环境', baseUrl: 'http://dev-api.example.com' },
        { id: 2, name: '测试环境', baseUrl: 'http://test-api.example.com' },
        { id: 3, name: '生产环境', baseUrl: 'http://api.example.com' }
      ]
      commit('SET_ENVIRONMENTS', mockEnvironments)

      console.log('环境列表加载完成:', mockEnvironments)
    } catch (error) {
      console.error('加载环境列表失败:', error)
      throw error
    }
  },

  selectEnvironment({ commit }, environmentId) {
    commit('SET_SELECTED_ENVIRONMENT_ID', environmentId)
    console.log('选择环境:', environmentId)
  },

  async executeRequest({ commit, state }, { tabId, requestData }) {
    try {
      commit('SET_EXECUTING_STATUS', true)

      // TODO: 实现执行请求的 API 调用
      // const response = await api.executeRequest(requestData)

      // 临时模拟响应数据
      const mockResponse = {
        status: 200,
        statusText: 'OK',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': '123'
        },
        data: {
          message: '请求执行成功',
          timestamp: new Date().toISOString()
        },
        time: Math.random() * 1000 + 100,
        size: 123
      }

      commit('SET_CURRENT_RESPONSE', mockResponse)
      console.log('请求执行完成:', mockResponse)

      return mockResponse
    } catch (error) {
      console.error('执行请求失败:', error)
      throw error
    } finally {
      commit('SET_EXECUTING_STATUS', false)
    }
  }
}

const modules = {}

export default {
  namespaced: true,
  actions,
  getters,
  state,
  mutations,
  modules
}
